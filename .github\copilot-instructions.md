---
type: "always_apply"
---

# OTA订单处理系统 - AI代理开发指南

这是一个专为GoMyHire构建的OTA订单智能处理系统，采用原生JavaScript模块化架构。

## 🏗️ 系统架构概览

### 核心架构模式
- **传统Script标签模块系统**: 使用`window.OTA`命名空间组织所有模块
- **事件驱动架构**: 模块间通过自定义事件通信，避免紧耦合
- **状态集中管理**: `app-state.js`作为单一数据源，所有状态变更通过它进行
- **分层服务架构**: UI层 → Manager层 → Service层 → API层

### 模块加载顺序（关键！）
HTML中script标签的加载顺序至关重要，必须严格按照以下顺序：
```html
<!-- 1. 基础工具和日志 -->
<script src="js/utils.js"></script>
<script src="js/logger.js"></script>
<!-- 2. 核心状态管理 -->
<script src="js/app-state.js"></script>
<!-- 3. 服务层 -->
<script src="js/api-service.js"></script>
<script src="js/gemini-service.js"></script>
<!-- 4. 管理器层 -->
<script src="js/managers/state-manager.js"></script>
<!-- 5. UI控制层 -->
<script src="js/ui-manager.js"></script>
<!-- 6. 主入口 -->
<script src="main.js"></script>
```

## 🧩 关键架构决策

### 命名空间模式
所有模块必须挂载到`window.OTA`命名空间：
```javascript
window.OTA = window.OTA || {};
window.OTA.moduleName = new ModuleClass();
```

### 依赖注入模式
由于script标签加载的异步性，使用延迟获取模式：
```javascript
function getAppState() {
    return window.OTA.appState || window.appState;
}
```

### 状态管理约定
- 所有状态变更必须通过`appState.setState()`方法
- 状态变更会自动触发相应的事件通知
- UI组件监听状态事件进行更新，不直接修改状态

## 🔧 开发工作流程

### 必读memory-bank文档
开始任何开发任务前，必须读取以下核心文档（按优先级）：
1. `memory-bank/activeContext.md` - 当前工作焦点
2. `memory-bank/systemPatterns.md` - 系统架构决策
3. `memory-bank/progress.md` - 项目进度和已知问题
4. `memory-bank/techContext.md` - 技术约束和配置

### 调试和测试模式
- **按钮诊断**: 使用`js/button-diagnostics.js`验证UI组件功能
- **实时监控**: `js/monitoring-wrapper.js`提供性能和错误监控
- **状态验证**: 通过浏览器控制台访问`window.OTA.appState.getState()`

### API集成模式
- **静态数据优先**: `api-service.js`包含所有静态映射数据，避免重复API调用
- **错误恢复**: 所有API调用都有fallback机制和重试逻辑
- **认证处理**: Token自动刷新和过期处理已内置

## 🎯 项目特定约定

### 文件组织规范
- `js/managers/` - 业务逻辑管理器（状态、事件、表单、价格）
- `js/` - 核心服务和工具类
- `docs/reports/` - 修复报告和系统文档
- `memory-bank/` - 项目上下文和知识库

### 错误处理模式
```javascript
// 标准错误处理模式
try {
    const result = await apiCall();
    logger.log('操作成功', 'success', result);
} catch (error) {
    logger.logError('操作失败', error);
    // 显示用户友好的错误信息
    uiManager.showError('操作失败，请重试');
}
```

### 国际化支持
- 使用`js/i18n.js`进行多语言支持
- 所有用户面向的文本都要通过i18n函数处理
- 默认语言为中文，支持英文切换

## 🚀 部署和环境

### Netlify配置要点
- **无构建步骤**: 纯静态文件部署
- **CSP策略**: 严格的内容安全策略，注意API域名白名单
- **缓存策略**: 静态资源永久缓存，HTML文件无缓存

### 环境变量和API密钥
- Gemini API密钥在运行时通过UI配置
- GoMyHire API使用用户提供的认证token
- 所有敏感信息不在代码中硬编码

## ⚠️ 关键注意事项

### 性能优化
- 大文件（>800行）需要拆分
- 避免循环依赖，特别注意manager层的相互引用
- 使用`utils.performanceMonitor`跟踪关键操作性能

### 安全考虑
- 所有用户输入都经过验证和清理
- API调用都有超时和错误边界保护
- 本地存储数据加密存储敏感信息

### 维护性原则
- 每个功能修改都要更新相关的报告文档
- 使用中文注释解释业务逻辑
- 保持单一职责原则，一个文件只做一件事

---

**记住**: 这是一个生产环境系统，任何修改都可能影响真实的订单处理。优先考虑稳定性和可维护性，而不是快速功能实现。