# OTA订单处理系统

GoMyHire Integration - 智能订单处理系统

## 功能特性

- 🤖 智能订单解析
- 📷 图片分析支持
- 🔢 多订单管理
- 📝 历史记录管理
- 🌐 多语言支持 (中文/英文)
- 🔄 实时数据同步

## 系统架构

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **AI服务**: Google Gemini API
- **后端API**: GoMyHire API
- **部署**: Netlify

## 部署信息

- **项目名称**: gmhcreateorder
- **站点ID**: 578f091d-52e6-405d-9001-b2f0801e4cbf
- **主域名**: gmhcreateorder.netlify.app
- **状态页面**: /status.html

## 文件结构

```
├── index.html          # 主应用页面
├── status.html         # 系统状态页面
├── style.css          # 样式文件
├── main.js            # 主要逻辑
├── js/                # JavaScript模块
│   ├── api-service.js
│   ├── gemini-service.js
│   └── ...
├── netlify.toml       # Netlify配置
└── package.json       # 项目配置
```

## 使用说明

1. 访问主页面进行订单创建
2. 使用 /status.html 查看系统状态
3. 支持图片上传和文本解析
4. 自动保存历史记录

## 版本信息

- **版本**: v1.0.0
- **最后更新**: 2025-07-09
- **部署状态**: 在线运行
