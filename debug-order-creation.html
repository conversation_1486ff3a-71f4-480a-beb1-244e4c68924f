<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单创建诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.success {
            background: #28a745;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .diagnostic-steps {
            list-style: none;
            padding: 0;
        }
        .diagnostic-steps li {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🔍 订单创建诊断工具</h1>
    
    <div class="diagnostic-panel">
        <h2>快速诊断</h2>
        <p>请按顺序点击以下按钮来诊断订单创建问题：</p>
        
        <button class="btn" onclick="checkMainApp()">1. 检查主应用状态</button>
        <button class="btn" onclick="checkFormData()">2. 检查表单数据</button>
        <button class="btn" onclick="testFormCollection()">3. 测试数据收集</button>
        <button class="btn" onclick="testApiCall()">4. 测试API调用</button>
        <button class="btn success" onclick="fullDiagnosis()">完整诊断</button>
        <button class="btn danger" onclick="clearOutput()">清除输出</button>
    </div>

    <div class="diagnostic-panel">
        <h2>诊断步骤说明</h2>
        <ol class="diagnostic-steps">
            <li><strong>检查主应用状态</strong> - 验证所有必需的模块是否已正确加载</li>
            <li><strong>检查表单数据</strong> - 验证表单元素是否存在并可访问</li>
            <li><strong>测试数据收集</strong> - 尝试从表单收集数据</li>
            <li><strong>测试API调用</strong> - 尝试发送订单创建请求</li>
        </ol>
    </div>

    <div class="diagnostic-panel">
        <h2>诊断输出</h2>
        <div id="output" class="log-output">等待诊断...</div>
    </div>

    <script>
        const output = document.getElementById('output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = {
                'info': 'ℹ️',
                'success': '✅', 
                'warning': '⚠️',
                'error': '❌'
            }[type] || 'ℹ️';
            
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            output.textContent = '';
        }

        async function checkMainApp() {
            log('开始检查主应用状态...');
            
            try {
                // 检查关键对象是否存在
                const checks = {
                    'window.OTA': !!window.OTA,
                    'window.OTA.uiManager': !!(window.OTA && window.OTA.uiManager),
                    'FormManager': !!(window.OTA && window.OTA.uiManager && window.OTA.uiManager.managers && window.OTA.uiManager.managers.form),
                    'ApiService': !!(window.OTA && window.OTA.apiService),
                    'AppState': !!(window.OTA && window.OTA.appState),
                    '创建订单按钮': !!document.getElementById('createOrder')
                };

                Object.entries(checks).forEach(([name, exists]) => {
                    log(`${name}: ${exists ? '存在' : '缺失'}`, exists ? 'success' : 'error');
                });

                const allOK = Object.values(checks).every(v => v);
                log(`主应用状态检查${allOK ? '完成' : '发现问题'}`, allOK ? 'success' : 'error');
                
                return allOK;
            } catch (error) {
                log(`主应用状态检查异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function checkFormData() {
            log('开始检查表单数据...');
            
            try {
                const requiredFields = [
                    'customerName', 'customerContact', 'pickup', 'dropoff',
                    'pickupDate', 'pickupTime', 'passengerCount', 'otaPrice', 'currency'
                ];

                let filledCount = 0;
                const uiManager = window.OTA && window.OTA.uiManager;
                
                if (!uiManager || !uiManager.elements) {
                    log('UI管理器或元素对象不可用', 'error');
                    return false;
                }

                requiredFields.forEach(fieldName => {
                    const element = uiManager.elements[fieldName];
                    if (element) {
                        const value = element.value || '';
                        const filled = value.trim() !== '';
                        log(`${fieldName}: ${filled ? '已填写' : '未填写'} (${value.slice(0, 20)}...)`, filled ? 'success' : 'warning');
                        if (filled) filledCount++;
                    } else {
                        log(`${fieldName}: 元素不存在`, 'error');
                    }
                });

                log(`表单检查完成: ${filledCount}/${requiredFields.length} 个必填字段已填写`);
                return filledCount > 0;
            } catch (error) {
                log(`表单数据检查异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function testFormCollection() {
            log('开始测试表单数据收集...');
            
            try {
                const uiManager = window.OTA && window.OTA.uiManager;
                const formManager = uiManager && uiManager.managers && uiManager.managers.form;
                
                if (!formManager) {
                    log('FormManager不可用', 'error');
                    return null;
                }

                const data = formManager.collectFormData();
                
                if (!data) {
                    log('收集到的数据为空', 'error');
                    return null;
                }

                const fieldCount = Object.keys(data).length;
                log(`成功收集到 ${fieldCount} 个字段的数据`, 'success');
                
                // 显示关键字段
                const keyFields = ['customer_name', 'customer_contact', 'pickup_location', 'ota_price'];
                keyFields.forEach(field => {
                    if (data[field]) {
                        log(`  ${field}: ${data[field]}`, 'info');
                    }
                });

                return data;
            } catch (error) {
                log(`表单数据收集异常: ${error.message}`, 'error');
                return null;
            }
        }

        async function testApiCall() {
            log('开始测试API调用...');
            
            try {
                const apiService = window.OTA && window.OTA.apiService;
                if (!apiService) {
                    log('ApiService不可用', 'error');
                    return false;
                }

                // 先收集数据
                const data = await testFormCollection();
                if (!data) {
                    log('无法获取表单数据，跳过API测试', 'warning');
                    return false;
                }

                log('开始发送API请求...', 'info');
                const result = await apiService.createOrder(data);
                
                if (result && result.success) {
                    log('API调用成功！', 'success');
                    log(`订单ID: ${result.data && result.data.id || '未知'}`, 'info');
                } else {
                    log(`API调用失败: ${result && result.message || '未知错误'}`, 'error');
                }
                
                return result && result.success;
            } catch (error) {
                log(`API调用异常: ${error.message}`, 'error');
                return false;
            }
        }

        async function fullDiagnosis() {
            log('=== 开始完整诊断 ===', 'info');
            clearOutput();
            
            await new Promise(resolve => setTimeout(resolve, 100));
            const step1 = await checkMainApp();
            
            await new Promise(resolve => setTimeout(resolve, 100));
            const step2 = await checkFormData();
            
            await new Promise(resolve => setTimeout(resolve, 100));
            const step3 = await testFormCollection();
            
            await new Promise(resolve => setTimeout(resolve, 100));
            const step4 = await testApiCall();
            
            log('=== 诊断完成 ===', 'info');
            const allPassed = step1 && step2 && step3 && step4;
            log(`总体状态: ${allPassed ? '正常' : '存在问题'}`, allPassed ? 'success' : 'error');
        }

        // 在页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('诊断工具已准备就绪', 'info');
                log('请点击上方按钮开始诊断，或访问主页面填写表单后再测试', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
