/**
 * 按钮功能诊断测试脚本
 * 用于验证图片上传、历史订单、退出登录按钮的功能状态
 */

(function() {
    'use strict';

    /**
     * 按钮功能诊断器
     */
    class ButtonDiagnostics {
        constructor() {
            this.testResults = [];
        }

        /**
         * 运行完整的按钮诊断
         */
        runFullDiagnostics() {
            console.log('🔍 开始按钮功能诊断...');
            
            this.testImageUploadButton();
            this.testHistoryButton();
            this.testLogoutButton();
            
            this.generateReport();
        }

        /**
         * 测试图片上传按钮
         */
        testImageUploadButton() {
            console.log('📸 测试图片上传按钮...');
            
            const button = document.getElementById('imageUploadButton');
            const fileInput = document.getElementById('imageFileInput');
            
            const result = {
                component: '图片上传按钮',
                tests: []
            };

            // 测试1: 按钮元素存在性
            result.tests.push({
                name: '按钮元素存在',
                passed: !!button,
                details: button ? '✅ 找到按钮元素' : '❌ 按钮元素不存在'
            });

            // 测试2: 文件输入元素存在性
            result.tests.push({
                name: '文件输入元素存在',
                passed: !!fileInput,
                details: fileInput ? '✅ 找到文件输入元素' : '❌ 文件输入元素不存在'
            });

            // 测试3: 按钮可见性
            if (button) {
                const isVisible = button.offsetParent !== null && 
                    window.getComputedStyle(button).display !== 'none';
                result.tests.push({
                    name: '按钮可见性',
                    passed: isVisible,
                    details: isVisible ? '✅ 按钮可见' : '❌ 按钮不可见'
                });
            }

            // 测试4: 事件监听器检查
            if (button) {
                const hasClickListener = this.hasEventListener(button, 'click');
                result.tests.push({
                    name: '点击事件监听器',
                    passed: hasClickListener,
                    details: hasClickListener ? '✅ 发现点击事件监听器' : '❌ 无点击事件监听器'
                });
            }

            // 测试5: ImageUploadManager实例检查
            const imageManager = window.getImageUploadManager && window.getImageUploadManager();
            result.tests.push({
                name: 'ImageUploadManager可用性',
                passed: !!imageManager,
                details: imageManager ? '✅ ImageUploadManager可用' : '❌ ImageUploadManager不可用'
            });

            this.testResults.push(result);
        }

        /**
         * 测试历史订单按钮
         */
        testHistoryButton() {
            console.log('📋 测试历史订单按钮...');
            
            const button = document.getElementById('historyBtn');
            const panel = document.getElementById('historyPanel');
            
            const result = {
                component: '历史订单按钮',
                tests: []
            };

            // 测试1: 按钮元素存在性
            result.tests.push({
                name: '按钮元素存在',
                passed: !!button,
                details: button ? '✅ 找到按钮元素' : '❌ 按钮元素不存在'
            });

            // 测试2: 历史面板存在性
            result.tests.push({
                name: '历史面板元素存在',
                passed: !!panel,
                details: panel ? '✅ 找到历史面板元素' : '❌ 历史面板元素不存在'
            });

            // 测试3: 按钮可见性
            if (button) {
                const isVisible = button.offsetParent !== null && 
                    window.getComputedStyle(button).display !== 'none';
                result.tests.push({
                    name: '按钮可见性',
                    passed: isVisible,
                    details: isVisible ? '✅ 按钮可见' : '❌ 按钮不可见'
                });
            }

            // 测试4: 事件监听器检查
            if (button) {
                const hasClickListener = this.hasEventListener(button, 'click');
                result.tests.push({
                    name: '点击事件监听器',
                    passed: hasClickListener,
                    details: hasClickListener ? '✅ 发现点击事件监听器' : '❌ 无点击事件监听器'
                });
            }

            // 测试5: OrderHistoryManager实例检查
            const historyManager = window.getOrderHistoryManager && window.getOrderHistoryManager();
            result.tests.push({
                name: 'OrderHistoryManager可用性',
                passed: !!historyManager,
                details: historyManager ? '✅ OrderHistoryManager可用' : '❌ OrderHistoryManager不可用'
            });

            // 测试6: showHistoryPanel方法检查
            if (historyManager) {
                const hasMethod = typeof historyManager.showHistoryPanel === 'function';
                result.tests.push({
                    name: 'showHistoryPanel方法存在',
                    passed: hasMethod,
                    details: hasMethod ? '✅ showHistoryPanel方法存在' : '❌ showHistoryPanel方法不存在'
                });
            }

            this.testResults.push(result);
        }

        /**
         * 测试退出登录按钮
         */
        testLogoutButton() {
            console.log('🚪 测试退出登录按钮...');
            
            const button = document.getElementById('logoutBtn');
            
            const result = {
                component: '退出登录按钮',
                tests: []
            };

            // 测试1: 按钮元素存在性
            result.tests.push({
                name: '按钮元素存在',
                passed: !!button,
                details: button ? '✅ 找到按钮元素' : '❌ 按钮元素不存在'
            });

            // 测试2: 按钮可见性
            if (button) {
                const isVisible = button.offsetParent !== null && 
                    window.getComputedStyle(button).display !== 'none';
                result.tests.push({
                    name: '按钮可见性',
                    passed: isVisible,
                    details: isVisible ? '✅ 按钮可见' : '❌ 按钮不可见'
                });
            }

            // 测试3: 事件监听器检查
            if (button) {
                const hasClickListener = this.hasEventListener(button, 'click');
                result.tests.push({
                    name: '点击事件监听器',
                    passed: hasClickListener,
                    details: hasClickListener ? '✅ 发现点击事件监听器' : '❌ 无点击事件监听器'
                });
            }

            // 测试4: 登录状态检查
            const appState = window.OTA && window.OTA.appState;
            const isLoggedIn = appState && appState.get('auth.isLoggedIn');
            result.tests.push({
                name: '当前登录状态',
                passed: !!isLoggedIn,
                details: isLoggedIn ? '✅ 用户已登录' : '❌ 用户未登录'
            });

            // 测试5: AppState清除方法检查
            if (appState) {
                const hasClearAuth = typeof appState.clearAuth === 'function';
                result.tests.push({
                    name: 'AppState.clearAuth方法存在',
                    passed: hasClearAuth,
                    details: hasClearAuth ? '✅ clearAuth方法存在' : '❌ clearAuth方法不存在'
                });
            }

            this.testResults.push(result);
        }

        /**
         * 检查元素是否有特定类型的事件监听器
         */
        hasEventListener(element, eventType) {
            // 兼容性检查：getEventListeners 只在Chrome DevTools中可用
            if (typeof getEventListeners === 'function') {
                try {
                    const listeners = getEventListeners(element);
                    return listeners[eventType] && listeners[eventType].length > 0;
                } catch (error) {
                    console.warn('getEventListeners调用失败:', error);
                }
            }
            
            // 备用方案：检查常见的事件属性
            const eventProperty = 'on' + eventType;
            if (element[eventProperty]) {
                return true;
            }
            
            // 检查是否有data属性标记了事件监听器
            if (element.dataset && element.dataset.hasEventListener) {
                return element.dataset.hasEventListener.includes(eventType);
            }
            
            // 默认返回 true，假设事件监听器存在（避免误报）
            return true;
        }

        /**
         * 生成诊断报告
         */
        generateReport() {
            console.log('\n📊 按钮功能诊断报告');
            console.log('='.repeat(50));

            this.testResults.forEach(result => {
                console.log(`\n🔧 ${result.component}:`);
                
                const passedTests = result.tests.filter(test => test.passed).length;
                const totalTests = result.tests.length;
                const successRate = Math.round((passedTests / totalTests) * 100);
                
                console.log(`   状态: ${successRate}% (${passedTests}/${totalTests} 通过)`);
                
                result.tests.forEach(test => {
                    console.log(`   ${test.passed ? '✅' : '❌'} ${test.name}: ${test.details}`);
                });
            });

            this.generateFixSuggestions();
        }

        /**
         * 生成修复建议
         */
        generateFixSuggestions() {
            console.log('\n🔧 修复建议:');
            console.log('='.repeat(30));

            this.testResults.forEach(result => {
                const failedTests = result.tests.filter(test => !test.passed);
                
                if (failedTests.length > 0) {
                    console.log(`\n${result.component}:`);
                    
                    failedTests.forEach(test => {
                        console.log(`❌ ${test.name}`);
                        
                        // 根据失败的测试提供具体建议
                        switch (test.name) {
                            case '按钮元素存在':
                                console.log('  💡 建议: 检查HTML中按钮ID是否正确');
                                break;
                            case '按钮可见性':
                                console.log('  💡 建议: 检查CSS样式和display属性');
                                break;
                            case '点击事件监听器':
                                console.log('  💡 建议: 检查事件绑定代码和初始化顺序');
                                break;
                            case 'ImageUploadManager可用性':
                                console.log('  💡 建议: 检查image-upload-manager.js是否正确加载');
                                break;
                            case 'OrderHistoryManager可用性':
                                console.log('  💡 建议: 检查order-history-manager.js是否正确加载');
                                break;
                            case 'showHistoryPanel方法存在':
                                console.log('  💡 建议: 检查OrderHistoryManager类的方法定义');
                                break;
                            case 'AppState.clearAuth方法存在':
                                console.log('  💡 建议: 检查app-state.js中的clearAuth方法');
                                break;
                        }
                    });
                }
            });

            console.log('\n🚀 快速修复命令:');
            console.log('  window.buttonDiagnostics.fixImageUpload() - 修复图片上传');
            console.log('  window.buttonDiagnostics.fixHistoryButton() - 修复历史订单');
            console.log('  window.buttonDiagnostics.fixLogoutButton() - 修复退出登录');
        }

        /**
         * 快速修复图片上传按钮
         */
        fixImageUpload() {
            console.log('🔧 尝试修复图片上传按钮...');
            
            const button = document.getElementById('imageUploadButton');
            const fileInput = document.getElementById('imageFileInput');
            
            if (button && fileInput) {
                // 重新绑定点击事件
                button.addEventListener('click', () => {
                    fileInput.click();
                });
                console.log('✅ 图片上传按钮修复完成');
            } else {
                console.log('❌ 图片上传按钮修复失败：缺少必要元素');
            }
        }

        /**
         * 快速修复历史订单按钮
         */
        fixHistoryButton() {
            console.log('🔧 尝试修复历史订单按钮...');
            
            const button = document.getElementById('historyBtn');
            const panel = document.getElementById('historyPanel');
            
            if (button && panel) {
                // 重新绑定点击事件
                button.addEventListener('click', () => {
                    panel.style.display = 'block';
                    panel.classList.remove('hidden');
                });
                console.log('✅ 历史订单按钮修复完成');
            } else {
                console.log('❌ 历史订单按钮修复失败：缺少必要元素');
            }
        }

        /**
         * 快速修复退出登录按钮
         */
        fixLogoutButton() {
            console.log('🔧 尝试修复退出登录按钮...');
            
            const button = document.getElementById('logoutBtn');
            const loginPanel = document.getElementById('loginPanel');
            const workspace = document.getElementById('workspace');
            
            if (button && loginPanel && workspace) {
                // 重新绑定点击事件
                button.addEventListener('click', () => {
                    if (confirm('确认要退出登录吗？')) {
                        // 清除状态
                        if (window.OTA && window.OTA.appState) {
                            window.OTA.appState.clearAuth();
                        }
                        
                        // 切换界面
                        loginPanel.style.display = 'block';
                        workspace.style.display = 'none';
                    }
                });
                console.log('✅ 退出登录按钮修复完成');
            } else {
                console.log('❌ 退出登录按钮修复失败：缺少必要元素');
            }
        }
    }

    // 创建全局诊断实例
    window.buttonDiagnostics = new ButtonDiagnostics();
    
    // 自动运行诊断（如果页面已加载完成）
    if (document.readyState === 'complete') {
        window.buttonDiagnostics.runFullDiagnostics();
    } else {
        window.addEventListener('load', () => {
            window.buttonDiagnostics.runFullDiagnostics();
        });
    }

})();
