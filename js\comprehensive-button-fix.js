/**
 * 综合按钮修复脚本
 * 解决图片上传、历史订单、退出登录按钮的所有问题
 */

(function() {
    'use strict';

    /**
     * 综合按钮修复器
     */
    class ButtonFixer {
        constructor() {
            this.fixAttempts = 0;
            this.maxAttempts = 3;
        }

        /**
         * 执行完整的按钮修复
         */
        async fixAllButtons() {
            console.log('🔧 开始综合按钮修复...');

            this.fixAttempts++;
            
            if (this.fixAttempts > this.maxAttempts) {
                console.error('❌ 达到最大修复尝试次数，修复终止');
                return;
            }

            // 等待DOM完全加载
            await this.waitForDOM();

            // 修复图片上传按钮
            this.fixImageUploadButton();

            // 修复历史订单按钮
            this.fixHistoryButton();

            // 修复退出登录按钮
            this.fixLogoutButton();

            // 验证修复结果
            setTimeout(() => {
                this.verifyFixes();
            }, 1000);

            console.log('✅ 综合按钮修复完成');
        }

        /**
         * 等待DOM元素加载
         */
        waitForDOM() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
        }

        /**
         * 修复图片上传按钮
         */
        fixImageUploadButton() {
            console.log('📸 修复图片上传按钮...');

            try {
                const button = document.getElementById('imageUploadButton');
                const fileInput = document.getElementById('imageFileInput');

                if (!button) {
                    console.error('❌ 图片上传按钮元素不存在');
                    return;
                }

                if (!fileInput) {
                    console.error('❌ 文件输入元素不存在');
                    return;
                }

                // 移除所有现有的点击监听器
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // 添加新的点击监听器
                newButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('🖱️ 图片上传按钮被点击');
                    
                    try {
                        fileInput.click();
                        console.log('✅ 文件选择对话框已打开');
                    } catch (error) {
                        console.error('❌ 打开文件选择对话框失败:', error);
                    }
                });

                // 确保按钮可见
                newButton.style.display = '';
                newButton.style.visibility = 'visible';
                newButton.disabled = false;

                console.log('✅ 图片上传按钮修复完成');

            } catch (error) {
                console.error('❌ 图片上传按钮修复失败:', error);
            }
        }

        /**
         * 修复历史订单按钮
         */
        fixHistoryButton() {
            console.log('📋 修复历史订单按钮...');

            try {
                const button = document.getElementById('historyBtn');
                const panel = document.getElementById('historyPanel');

                if (!button) {
                    console.error('❌ 历史订单按钮元素不存在');
                    return;
                }

                if (!panel) {
                    console.error('❌ 历史订单面板元素不存在');
                    return;
                }

                // 移除所有现有的点击监听器
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // 添加新的点击监听器
                newButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('🖱️ 历史订单按钮被点击');
                    
                    try {
                        // 方法1: 尝试使用OrderHistoryManager
                        if (this.tryOrderHistoryManager(panel)) {
                            return;
                        }

                        // 方法2: 直接操作DOM
                        this.directShowHistoryPanel(panel);

                    } catch (error) {
                        console.error('❌ 显示历史订单面板失败:', error);
                    }
                });

                console.log('✅ 历史订单按钮修复完成');

            } catch (error) {
                console.error('❌ 历史订单按钮修复失败:', error);
            }
        }

        /**
         * 尝试使用OrderHistoryManager显示面板
         */
        tryOrderHistoryManager(panel) {
            try {
                let historyManager = null;

                // 尝试多种方式获取OrderHistoryManager
                if (window.getOrderHistoryManager) {
                    historyManager = window.getOrderHistoryManager();
                } else if (window.OTA && window.OTA.orderHistoryManager) {
                    historyManager = window.OTA.orderHistoryManager;
                } else if (window.OrderHistoryManager) {
                    historyManager = new window.OrderHistoryManager();
                }

                if (historyManager && typeof historyManager.showHistoryPanel === 'function') {
                    historyManager.showHistoryPanel();
                    console.log('✅ 使用OrderHistoryManager显示面板');
                    return true;
                }

                return false;

            } catch (error) {
                console.warn('⚠️ OrderHistoryManager方法失败:', error);
                return false;
            }
        }

        /**
         * 直接显示历史订单面板
         */
        directShowHistoryPanel(panel) {
            panel.style.display = 'block';
            panel.classList.remove('hidden');
            
            // 添加模态框效果
            panel.style.position = 'fixed';
            panel.style.top = '0';
            panel.style.left = '0';
            panel.style.width = '100%';
            panel.style.height = '100%';
            panel.style.zIndex = '1000';
            panel.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';

            console.log('✅ 直接显示历史订单面板');

            // 添加关闭按钮事件
            const closeBtn = panel.querySelector('#closeHistoryBtn, .close-btn, [data-action="close"]');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    panel.style.display = 'none';
                    panel.classList.add('hidden');
                });
            }
        }

        /**
         * 修复退出登录按钮
         */
        fixLogoutButton() {
            console.log('🚪 修复退出登录按钮...');

            try {
                const button = document.getElementById('logoutBtn');
                const loginPanel = document.getElementById('loginPanel');
                const workspace = document.getElementById('workspace');

                if (!button) {
                    console.error('❌ 退出登录按钮元素不存在');
                    return;
                }

                if (!loginPanel || !workspace) {
                    console.error('❌ 必要的界面元素不存在');
                    return;
                }

                // 移除所有现有的点击监听器
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // 添加新的点击监听器
                newButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('🖱️ 退出登录按钮被点击');
                    
                    // 显示确认对话框
                    if (confirm('确认要退出登录吗？这将清除当前的登录状态并返回登录界面。')) {
                        this.performLogout(loginPanel, workspace);
                    } else {
                        console.log('ℹ️ 用户取消了登出操作');
                    }
                });

                console.log('✅ 退出登录按钮修复完成');

            } catch (error) {
                console.error('❌ 退出登录按钮修复失败:', error);
            }
        }

        /**
         * 执行登出操作
         */
        performLogout(loginPanel, workspace) {
            try {
                console.log('🔄 执行登出操作...');

                // 清除应用状态
                if (window.OTA && window.OTA.appState) {
                    if (typeof window.OTA.appState.clearAuth === 'function') {
                        window.OTA.appState.clearAuth();
                        console.log('✅ 应用状态已清除');
                    }
                }

                // 清除API服务的认证数据
                if (window.OTA && window.OTA.apiService) {
                    if (typeof window.OTA.apiService.clearAuthData === 'function') {
                        window.OTA.apiService.clearAuthData();
                        console.log('✅ API认证数据已清除');
                    }
                }

                // 切换界面
                loginPanel.style.display = 'block';
                workspace.style.display = 'none';

                // 隐藏用户信息区域
                const userInfo = document.querySelector('.user-info');
                if (userInfo) {
                    userInfo.style.display = 'none';
                }

                // 重置表单
                const loginForm = document.getElementById('loginForm');
                if (loginForm) {
                    loginForm.reset();
                }

                // 清空订单输入
                const orderInput = document.getElementById('orderInput');
                if (orderInput) {
                    orderInput.value = '';
                }

                console.log('✅ 登出操作完成');

                // 显示成功消息
                setTimeout(() => {
                    alert('已成功登出！');
                }, 100);

            } catch (error) {
                console.error('❌ 登出操作失败:', error);
                alert('登出过程中发生错误，请刷新页面重试。');
            }
        }

        /**
         * 验证修复结果
         */
        verifyFixes() {
            console.log('🔍 验证修复结果...');

            const checks = [
                {
                    name: '图片上传按钮',
                    element: document.getElementById('imageUploadButton'),
                    test: (el) => el && this.hasClickListener(el)
                },
                {
                    name: '历史订单按钮',
                    element: document.getElementById('historyBtn'),
                    test: (el) => el && this.hasClickListener(el)
                },
                {
                    name: '退出登录按钮',
                    element: document.getElementById('logoutBtn'),
                    test: (el) => el && this.hasClickListener(el)
                }
            ];

            let allPassed = true;

            checks.forEach(check => {
                const passed = check.test(check.element);
                console.log(`${passed ? '✅' : '❌'} ${check.name}: ${passed ? '正常' : '异常'}`);
                
                if (!passed) {
                    allPassed = false;
                }
            });

            if (allPassed) {
                console.log('🎉 所有按钮修复验证通过！');
            } else {
                console.warn('⚠️ 部分按钮仍有问题，建议刷新页面后重试');
            }
        }

        /**
         * 简单检查元素是否有点击监听器
         */
        hasClickListener(element) {
            // 这是一个简化的检查方法
            // 实际上很难准确检测事件监听器
            return element && element.onclick !== null || 
                   (element._listeners && element._listeners.click) ||
                   element.hasAttribute('onclick');
        }
    }

    // 创建全局修复器实例
    window.buttonFixer = new ButtonFixer();

    // 自动运行修复（延迟执行确保所有模块加载完成）
    setTimeout(() => {
        if (document.readyState === 'complete') {
            window.buttonFixer.fixAllButtons();
        } else {
            window.addEventListener('load', () => {
                window.buttonFixer.fixAllButtons();
            });
        }
    }, 2000);

    // 提供手动修复命令
    console.log('🔧 综合按钮修复器已加载');
    console.log('📋 手动修复命令: window.buttonFixer.fixAllButtons()');

})();
