/**
 * 网络连接诊断工具
 * 用于测试API连接和网络状态
 */

window.OTA = window.OTA || {};

(function() {
    'use strict';

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    function getApiService() {
        return window.OTA.apiService || window.apiService;
    }

    class NetworkDiagnostics {
        constructor() {
            this.apiService = null;
        }

        init() {
            this.apiService = getApiService();
            console.log('🌐 网络诊断工具已初始化');
        }

        // 基础网络连接测试
        async testBasicConnectivity() {
            console.group('🌐 基础网络连接测试');
            
            try {
                // 测试网络状态
                console.log('在线状态:', navigator.onLine ? '✅ 在线' : '❌ 离线');
                
                // 测试DNS解析
                console.log('测试DNS解析...');
                const dnsTest = await fetch('https://dns.google.com/resolve?name=gomyhire.com.my&type=A', {
                    method: 'GET',
                    mode: 'cors'
                }).catch(e => ({ error: e.message }));
                
                if (dnsTest.error) {
                    console.log('DNS测试:', '❌ 失败', dnsTest.error);
                } else {
                    console.log('DNS测试:', '✅ 成功');
                }

                // 测试基本HTTP连接
                console.log('测试HTTP连接...');
                const httpTest = await fetch('https://httpbin.org/get', {
                    method: 'GET',
                    mode: 'cors'
                }).catch(e => ({ error: e.message }));
                
                if (httpTest.error) {
                    console.log('HTTP测试:', '❌ 失败', httpTest.error);
                } else {
                    console.log('HTTP测试:', '✅ 成功');
                }

            } catch (error) {
                console.error('基础连接测试异常:', error);
            } finally {
                console.groupEnd();
            }
        }

        // 测试目标API服务器连接
        async testApiServerConnectivity() {
            console.group('🚀 API服务器连接测试');
            
            if (!this.apiService) {
                console.error('❌ ApiService不可用');
                console.groupEnd();
                return;
            }

            const baseURL = this.apiService.baseURL;
            console.log('目标API地址:', baseURL);

            try {
                // 测试服务器可达性（简单的HEAD请求）
                console.log('测试服务器可达性...');
                const reachabilityTest = await fetch(baseURL, {
                    method: 'HEAD',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json'
                    }
                }).catch(e => ({ error: e.message }));

                if (reachabilityTest.error) {
                    console.log('服务器可达性:', '❌ 不可达', reachabilityTest.error);
                } else {
                    console.log('服务器可达性:', '✅ 可达', `状态: ${reachabilityTest.status}`);
                }

                // 测试CORS预检请求
                console.log('测试CORS预检...');
                const corsTest = await fetch(`${baseURL}/create_order`, {
                    method: 'OPTIONS',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                }).catch(e => ({ error: e.message }));

                if (corsTest.error) {
                    console.log('CORS预检:', '❌ 失败', corsTest.error);
                } else {
                    console.log('CORS预检:', '✅ 成功', `状态: ${corsTest.status}`);
                }

            } catch (error) {
                console.error('API服务器连接测试异常:', error);
            } finally {
                console.groupEnd();
            }
        }

        // 测试订单创建API端点
        async testCreateOrderEndpoint() {
            console.group('📋 订单创建端点测试');

            if (!this.apiService) {
                console.error('❌ ApiService不可用');
                console.groupEnd();
                return;
            }

            try {
                // 创建测试数据
                const testData = {
                    customer_name: 'Test Customer',
                    customer_contact: '+60123456789',
                    customer_email: '<EMAIL>',
                    pickup_location: 'KLIA Airport',
                    dropoff_location: 'KL Sentral',
                    pickup_date: '2025-07-15',
                    pickup_time: '10:00',
                    passenger_number: '2',
                    luggage_count: '2',
                    ota_price: '150.00',
                    currency: 'MYR',
                    sub_category_id: '1',
                    car_type_id: '1',
                    incharge_by_backend_user_id: '1',
                    driving_region_id: '1'
                };

                console.log('测试数据:', testData);

                // 发送测试请求
                console.log('发送测试订单创建请求...');
                const response = await fetch(`${this.apiService.baseURL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData),
                    mode: 'cors'
                });

                console.log('响应状态:', response.status, response.statusText);
                console.log('响应头:', Object.fromEntries(response.headers.entries()));

                const responseText = await response.text();
                console.log('响应内容:', responseText);

                try {
                    const responseData = JSON.parse(responseText);
                    console.log('解析后的响应数据:', responseData);
                } catch (parseError) {
                    console.log('响应不是有效的JSON格式');
                }

                if (response.ok) {
                    console.log('✅ 端点测试成功');
                } else {
                    console.log('❌ 端点测试失败');
                }

            } catch (error) {
                console.error('❌ 订单创建端点测试异常:', error);
                console.error('错误详情:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
            } finally {
                console.groupEnd();
            }
        }

        // 完整的网络诊断
        async runFullDiagnostics() {
            console.log('🔍 开始完整网络诊断...');
            
            await this.testBasicConnectivity();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await this.testApiServerConnectivity();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await this.testCreateOrderEndpoint();
            
            console.log('🔍 网络诊断完成');
        }

        // 检查浏览器兼容性
        checkBrowserCompatibility() {
            console.group('🌐 浏览器兼容性检查');
            
            const features = {
                'Fetch API': 'fetch' in window,
                'Promise': 'Promise' in window,
                'async/await': (async () => {})().constructor === Promise,
                'AbortController': 'AbortController' in window,
                'JSON': 'JSON' in window,
                'localStorage': 'localStorage' in window,
                'console': 'console' in window
            };

            Object.entries(features).forEach(([feature, supported]) => {
                console.log(`${feature}: ${supported ? '✅ 支持' : '❌ 不支持'}`);
            });

            console.log('用户代理:', navigator.userAgent);
            console.groupEnd();
        }
    }

    // 创建全局实例
    window.OTA.networkDiagnostics = new NetworkDiagnostics();
    window.netDiag = window.OTA.networkDiagnostics; // 简化访问

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                window.netDiag.init();
            }, 2000);
        });
    } else {
        setTimeout(() => {
            window.netDiag.init();
        }, 2000);
    }

    console.log('🌐 网络诊断工具已加载！');
    console.log('使用方法:');
    console.log('- netDiag.runFullDiagnostics() - 运行完整网络诊断');
    console.log('- netDiag.testBasicConnectivity() - 测试基础网络连接');
    console.log('- netDiag.testApiServerConnectivity() - 测试API服务器连接');
    console.log('- netDiag.testCreateOrderEndpoint() - 测试订单创建端点');
    console.log('- netDiag.checkBrowserCompatibility() - 检查浏览器兼容性');

})();
