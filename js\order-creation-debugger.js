/**
 * 订单创建调试器
 * 用于诊断订单创建过程中的错误
 */

window.OTA = window.OTA || {};

(function() {
    'use strict';

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    function getApiService() {
        return window.OTA.apiService || window.apiService;
    }

    function getAppState() {
        return window.OTA.appState || window.appState;
    }

    class OrderCreationDebugger {
        constructor() {
            this.isEnabled = false;
            this.logs = [];
        }

        enable() {
            this.isEnabled = true;
            this.setupDebugInterceptors();
            console.log('🔍 订单创建调试器已启用');
        }

        disable() {
            this.isEnabled = false;
            console.log('🔍 订单创建调试器已禁用');
        }

        log(message, data = null) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                message,
                data
            };
            this.logs.push(logEntry);
            console.log(`🔍 [${logEntry.timestamp}] ${message}`, data);
        }

        setupDebugInterceptors() {
            // 拦截创建订单按钮点击
            const createBtn = document.getElementById('createOrder');
            if (createBtn) {
                const originalHandler = createBtn.onclick;
                createBtn.onclick = (e) => {
                    this.log('创建订单按钮被点击');
                    if (originalHandler) {
                        return originalHandler.call(createBtn, e);
                    }
                };
            }

            // 拦截表单数据收集
            const formManager = window.OTA.uiManager?.managers?.form;
            if (formManager && formManager.collectFormData) {
                const originalCollectFormData = formManager.collectFormData.bind(formManager);
                formManager.collectFormData = () => {
                    this.log('开始收集表单数据');
                    try {
                        const data = originalCollectFormData();
                        this.log('表单数据收集成功', {
                            dataKeys: Object.keys(data),
                            dataSize: Object.keys(data).length,
                            hasRequiredFields: this.checkRequiredFields(data)
                        });
                        return data;
                    } catch (error) {
                        this.log('表单数据收集失败', { error: error.message });
                        throw error;
                    }
                };
            }

            // 拦截API请求
            const apiService = getApiService();
            if (apiService && apiService.createOrder) {
                const originalCreateOrder = apiService.createOrder.bind(apiService);
                apiService.createOrder = async (orderData) => {
                    this.log('开始API订单创建请求', {
                        dataKeys: Object.keys(orderData),
                        url: `${apiService.baseURL}/create_order`
                    });
                    
                    try {
                        const result = await originalCreateOrder(orderData);
                        this.log('API订单创建响应', {
                            success: result.success,
                            hasData: !!result.data,
                            hasErrors: !!result.errors,
                            message: result.message
                        });
                        return result;
                    } catch (error) {
                        this.log('API订单创建异常', {
                            errorMessage: error.message,
                            errorName: error.name,
                            stack: error.stack
                        });
                        throw error;
                    }
                };
            }

            // 拦截UI错误显示
            const uiManager = window.OTA.uiManager;
            if (uiManager && uiManager.showAlert) {
                const originalShowAlert = uiManager.showAlert.bind(uiManager);
                uiManager.showAlert = (message, type, duration) => {
                    this.log('UI显示错误提示', {
                        message,
                        type,
                        duration,
                        stackTrace: new Error().stack
                    });
                    return originalShowAlert(message, type, duration);
                };
            }

            // 拦截事件管理器的订单创建处理
            const eventManager = window.OTA.uiManager?.managers?.event;
            if (eventManager && eventManager.handleCreateOrder) {
                const originalHandleCreateOrder = eventManager.handleCreateOrder.bind(eventManager);
                eventManager.handleCreateOrder = async (e) => {
                    this.log('事件管理器开始处理订单创建');
                    try {
                        const result = await originalHandleCreateOrder(e);
                        this.log('事件管理器处理订单创建完成');
                        return result;
                    } catch (error) {
                        this.log('事件管理器处理订单创建异常', {
                            errorMessage: error.message,
                            errorName: error.name
                        });
                        throw error;
                    }
                };
            }
        }

        checkRequiredFields(data) {
            const requiredFields = [
                'customer_name', 'customer_contact', 'pickup_location', 
                'dropoff_location', 'pickup_date', 'pickup_time', 
                'passenger_number', 'ota_price', 'currency'
            ];
            
            const missing = requiredFields.filter(field => !data[field] || !data[field].toString().trim());
            return {
                allPresent: missing.length === 0,
                missing: missing,
                present: requiredFields.filter(field => data[field] && data[field].toString().trim())
            };
        }

        // 手动测试订单创建流程
        async testOrderCreation() {
            this.log('开始手动测试订单创建流程');
            
            try {
                // 1. 检查系统状态
                this.log('检查系统状态');
                const systemStatus = this.checkSystemStatus();
                if (!systemStatus.allOK) {
                    throw new Error(`系统状态检查失败: ${systemStatus.issues.join(', ')}`);
                }

                // 2. 检查用户登录状态
                this.log('检查登录状态');
                const isLoggedIn = getAppState().get('auth.isLoggedIn');
                if (!isLoggedIn) {
                    this.log('用户未登录，但订单创建可能不需要登录');
                }

                // 3. 收集表单数据
                this.log('收集表单数据');
                const formManager = window.OTA.uiManager.managers.form;
                const orderData = formManager.collectFormData();
                
                if (!orderData || Object.keys(orderData).length === 0) {
                    throw new Error('无法收集表单数据或数据为空');
                }

                // 4. 验证表单数据
                this.log('验证表单数据');
                const requiredCheck = this.checkRequiredFields(orderData);
                if (!requiredCheck.allPresent) {
                    this.log('缺少必填字段', requiredCheck);
                    // 不抛出异常，继续测试
                }

                // 5. API验证
                this.log('执行API验证');
                const validation = getApiService().validateOrderData(orderData);
                if (!validation.isValid) {
                    this.log('API验证失败', validation);
                    // 不抛出异常，继续测试
                }

                // 6. 尝试创建订单
                this.log('尝试创建订单');
                const result = await getApiService().createOrder(orderData);
                
                if (result.success) {
                    this.log('✅ 订单创建测试成功', result);
                } else {
                    this.log('❌ 订单创建测试失败', result);
                }

                return result;

            } catch (error) {
                this.log('❌ 测试过程异常', {
                    errorMessage: error.message,
                    errorStack: error.stack
                });
                throw error;
            }
        }

        checkSystemStatus() {
            const checks = {
                uiManager: !!window.OTA.uiManager,
                formManager: !!(window.OTA.uiManager?.managers?.form),
                eventManager: !!(window.OTA.uiManager?.managers?.event),
                apiService: !!getApiService(),
                logger: !!getLogger(),
                appState: !!getAppState(),
                createButton: !!document.getElementById('createOrder')
            };

            const issues = Object.entries(checks)
                .filter(([key, value]) => !value)
                .map(([key]) => key);

            return {
                allOK: issues.length === 0,
                checks,
                issues
            };
        }

        // 检查表单状态
        checkFormStatus() {
            this.log('开始检查表单状态');
            
            const formElements = [
                'customerName', 'customerContact', 'customerEmail',
                'pickup', 'dropoff', 'pickupDate', 'pickupTime',
                'passengerCount', 'luggageCount', 'flightInfo',
                'otaPrice', 'currency', 'otaReferenceNumber',
                'subCategoryId', 'carTypeId', 'inchargeByBackendUserId',
                'drivingRegionId'
            ];
            
            const uiManager = window.OTA.uiManager;
            if (!uiManager || !uiManager.elements) {
                this.log('❌ UI管理器或元素不可用');
                return false;
            }

            const formStatus = {};
            formElements.forEach(elementName => {
                const element = uiManager.elements[elementName];
                if (element) {
                    const value = element.value || '';
                    formStatus[elementName] = {
                        exists: true,
                        value: value,
                        filled: value.trim() !== ''
                    };
                } else {
                    formStatus[elementName] = {
                        exists: false,
                        value: null,
                        filled: false
                    };
                }
            });
            
            this.log('表单状态检查完成', formStatus);
            return formStatus;
        }

        // 显示调试日志
        showLogs() {
            console.group('🔍 订单创建调试日志');
            this.logs.forEach(log => {
                console.log(`[${log.timestamp}] ${log.message}`, log.data);
            });
            console.groupEnd();
        }

        // 清除日志
        clearLogs() {
            this.logs = [];
            console.log('🔍 调试日志已清除');
        }

        // 生成诊断报告
        generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                systemStatus: this.checkSystemStatus(),
                formStatus: this.checkFormStatus(),
                logs: this.logs
            };
            
            console.group('🔍 订单创建诊断报告');
            console.log('系统状态:', report.systemStatus);
            console.log('表单状态:', report.formStatus);
            console.log('操作日志:', report.logs);
            console.groupEnd();
            
            return report;
        }
    }

    // 创建全局实例
    window.OTA.orderCreationDebugger = new OrderCreationDebugger();
    window.orderDebugger = window.OTA.orderCreationDebugger; // 简化访问

    // 等待页面加载完成后启用
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                window.orderDebugger.enable();
            }, 1000);
        });
    } else {
        setTimeout(() => {
            window.orderDebugger.enable();
        }, 1000);
    }

    // 添加快捷命令到控制台
    console.log('🔍 订单创建调试器已加载！');
    console.log('使用方法:');
    console.log('- orderDebugger.enable() - 启用调试拦截器');
    console.log('- orderDebugger.checkFormStatus() - 检查表单状态');
    console.log('- orderDebugger.testOrderCreation() - 手动测试订单创建');
    console.log('- orderDebugger.generateReport() - 生成诊断报告');
    console.log('- orderDebugger.showLogs() - 显示调试日志');
    console.log('- orderDebugger.clearLogs() - 清除调试日志');
    console.log('- orderDebugger.disable() - 禁用调试拦截器');

})();
