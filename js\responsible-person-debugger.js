/**
 * 负责人字段调试和修复脚本
 * 用于诊断和修复"负责人为必填项"的问题
 */

(function() {
    'use strict';

    /**
     * 负责人字段调试器
     */
    class ResponsiblePersonDebugger {
        constructor() {
            this.currentUser = null;
            this.backendUsers = [];
            this.defaultUserId = null;
        }

        /**
         * 运行完整的负责人字段诊断
         */
        runDiagnostics() {
            console.log('🔍 开始负责人字段诊断...');
            
            this.checkCurrentUser();
            this.checkBackendUsers();
            this.checkDefaultUserId();
            this.checkFormField();
            this.testCollectFormData();
            
            this.generateFixSuggestions();
        }

        /**
         * 检查当前登录用户
         */
        checkCurrentUser() {
            console.log('\n👤 检查当前登录用户...');
            
            try {
                const appState = window.OTA && window.OTA.appState;
                if (!appState) {
                    console.error('❌ AppState不可用');
                    return;
                }

                this.currentUser = appState.get('auth.user');
                if (this.currentUser) {
                    console.log('✅ 当前用户信息:');
                    console.log(`   邮箱: ${this.currentUser.email}`);
                    console.log(`   姓名: ${this.currentUser.name || '未知'}`);
                    console.log(`   ID: ${this.currentUser.id || '未知'}`);
                } else {
                    console.error('❌ 无当前登录用户信息');
                }
            } catch (error) {
                console.error('❌ 检查当前用户失败:', error);
            }
        }

        /**
         * 检查后台用户数据
         */
        checkBackendUsers() {
            console.log('\n👥 检查后台用户数据...');
            
            try {
                const appState = window.OTA && window.OTA.appState;
                if (!appState) {
                    console.error('❌ AppState不可用');
                    return;
                }

                this.backendUsers = appState.get('systemData.backendUsers') || [];
                
                if (this.backendUsers.length > 0) {
                    console.log(`✅ 找到 ${this.backendUsers.length} 个后台用户:`);
                    this.backendUsers.slice(0, 5).forEach((user, index) => {
                        console.log(`   ${index + 1}. ${user.name} (ID: ${user.id}, 邮箱: ${user.email || '无'})`);
                    });
                    
                    if (this.backendUsers.length > 5) {
                        console.log(`   ... 还有 ${this.backendUsers.length - 5} 个用户`);
                    }
                } else {
                    console.error('❌ 未找到后台用户数据');
                }
            } catch (error) {
                console.error('❌ 检查后台用户数据失败:', error);
            }
        }

        /**
         * 检查默认用户ID获取逻辑
         */
        checkDefaultUserId() {
            console.log('\n🎯 检查默认用户ID获取逻辑...');
            
            try {
                const apiService = window.OTA && window.OTA.apiService;
                if (!apiService) {
                    console.error('❌ API服务不可用');
                    return;
                }

                if (typeof apiService.getDefaultBackendUserId !== 'function') {
                    console.error('❌ getDefaultBackendUserId方法不存在');
                    return;
                }

                this.defaultUserId = apiService.getDefaultBackendUserId();
                
                if (this.defaultUserId) {
                    console.log(`✅ 获取到默认用户ID: ${this.defaultUserId}`);
                    
                    // 查找对应的用户信息
                    const matchedUser = this.backendUsers.find(u => u.id == this.defaultUserId);
                    if (matchedUser) {
                        console.log(`✅ 对应用户: ${matchedUser.name} (${matchedUser.email || '无邮箱'})`);
                    } else {
                        console.warn(`⚠️ 未找到ID为 ${this.defaultUserId} 的用户信息`);
                    }
                } else {
                    console.error('❌ 无法获取默认用户ID');
                }
            } catch (error) {
                console.error('❌ 检查默认用户ID失败:', error);
            }
        }

        /**
         * 检查表单中的负责人字段
         */
        checkFormField() {
            console.log('\n📝 检查表单中的负责人字段...');
            
            try {
                const field = document.getElementById('inchargeByBackendUserId');
                
                if (field) {
                    console.log('✅ 找到负责人字段元素');
                    console.log(`   当前值: ${field.value || '空'}`);
                    console.log(`   选项数量: ${field.options ? field.options.length : '非选择框'}`);
                    
                    if (field.options && field.options.length > 0) {
                        console.log('   前5个选项:');
                        Array.from(field.options).slice(0, 5).forEach((option, index) => {
                            console.log(`     ${index + 1}. ${option.text} (值: ${option.value})`);
                        });
                    }
                } else {
                    console.error('❌ 未找到负责人字段元素 (#inchargeByBackendUserId)');
                }
            } catch (error) {
                console.error('❌ 检查表单字段失败:', error);
            }
        }

        /**
         * 测试表单数据收集
         */
        testCollectFormData() {
            console.log('\n🔬 测试表单数据收集...');
            
            try {
                const uiManager = window.OTA && window.OTA.uiManager;
                if (!uiManager) {
                    console.error('❌ UIManager不可用');
                    return;
                }

                const formManager = uiManager.getManager('form');
                if (!formManager) {
                    console.error('❌ FormManager不可用');
                    return;
                }

                if (typeof formManager.collectFormData !== 'function') {
                    console.error('❌ collectFormData方法不存在');
                    return;
                }

                const formData = formManager.collectFormData();
                
                console.log('✅ 表单数据收集完成');
                console.log(`   负责人ID: ${formData.incharge_by_backend_user_id || '未设置'}`);
                console.log(`   OTA参考号: ${formData.ota_reference_number || '未设置'}`);
                console.log(`   子分类ID: ${formData.sub_category_id || '未设置'}`);
                console.log(`   车型ID: ${formData.car_type_id || '未设置'}`);
                
            } catch (error) {
                console.error('❌ 测试表单数据收集失败:', error);
            }
        }

        /**
         * 生成修复建议
         */
        generateFixSuggestions() {
            console.log('\n🔧 修复建议:');
            console.log('='.repeat(30));

            if (!this.currentUser) {
                console.log('❌ 问题: 未找到当前登录用户');
                console.log('   解决方案: 检查登录状态和认证流程');
            }

            if (this.backendUsers.length === 0) {
                console.log('❌ 问题: 未加载后台用户数据');
                console.log('   解决方案: 检查系统数据加载和API调用');
            }

            if (!this.defaultUserId) {
                console.log('❌ 问题: 无法获取默认用户ID');
                console.log('   解决方案: 检查getDefaultBackendUserId方法逻辑');
            }

            const field = document.getElementById('inchargeByBackendUserId');
            if (!field) {
                console.log('❌ 问题: 负责人字段元素不存在');
                console.log('   解决方案: 检查HTML结构和元素ID');
            }

            console.log('\n🚀 快速修复命令:');
            console.log('  fixResponsiblePerson() - 强制设置负责人字段');
            console.log('  reloadBackendUsers() - 重新加载后台用户数据');
        }

        /**
         * 强制修复负责人字段
         */
        fixResponsiblePerson() {
            console.log('🔧 开始强制修复负责人字段...');
            
            try {
                let userId = this.defaultUserId;
                
                if (!userId && this.backendUsers.length > 0) {
                    userId = this.backendUsers[0].id;
                    console.log(`使用第一个后台用户ID: ${userId}`);
                }
                
                if (!userId) {
                    userId = 1;
                    console.log('使用紧急默认ID: 1');
                }

                const field = document.getElementById('inchargeByBackendUserId');
                if (field) {
                    field.value = userId;
                    console.log(`✅ 已设置负责人字段值: ${userId}`);
                }

                // 测试表单数据收集
                const uiManager = window.OTA && window.OTA.uiManager;
                const formManager = uiManager && uiManager.getManager('form');
                if (formManager) {
                    const formData = formManager.collectFormData();
                    console.log(`✅ 表单数据中的负责人ID: ${formData.incharge_by_backend_user_id}`);
                }

            } catch (error) {
                console.error('❌ 修复失败:', error);
            }
        }

        /**
         * 重新加载后台用户数据
         */
        async reloadBackendUsers() {
            console.log('🔄 重新加载后台用户数据...');
            
            try {
                const apiService = window.OTA && window.OTA.apiService;
                if (!apiService) {
                    console.error('❌ API服务不可用');
                    return;
                }

                await apiService.getAllSystemData();
                console.log('✅ 系统数据重新加载完成');
                
                // 重新检查
                this.checkBackendUsers();
                this.checkDefaultUserId();
                
            } catch (error) {
                console.error('❌ 重新加载失败:', error);
            }
        }
    }

    // 创建全局实例
    window.responsiblePersonDebugger = new ResponsiblePersonDebugger();
    
    // 提供快捷方法
    window.fixResponsiblePerson = () => window.responsiblePersonDebugger.fixResponsiblePerson();
    window.reloadBackendUsers = () => window.responsiblePersonDebugger.reloadBackendUsers();
    
    console.log('🔧 负责人字段调试器已加载');
    console.log('📋 可用命令:');
    console.log('  window.responsiblePersonDebugger.runDiagnostics() - 运行完整诊断');
    console.log('  fixResponsiblePerson() - 强制修复负责人字段');
    console.log('  reloadBackendUsers() - 重新加载后台用户数据');

})();
