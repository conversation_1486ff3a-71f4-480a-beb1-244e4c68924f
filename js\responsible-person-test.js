/**
 * 负责人字段测试与验证脚本
 * 专门测试"负责人为必填项"修复效果
 */

(function() {
    'use strict';

    /**
     * 负责人字段测试器
     */
    class ResponsiblePersonTester {
        constructor() {
            this.testResults = [];
        }

        /**
         * 运行完整测试套件
         */
        async runFullTest() {
            console.log('\n🧪 负责人字段测试开始');
            console.log('='.repeat(50));

            this.testResults = [];

            // 测试1: DOM元素检查
            this.testDOMElements();

            // 测试2: 字段值设置
            this.testFieldValueSetting();

            // 测试3: 登录状态处理
            this.testLoginStateHandling();

            // 测试4: 表单提交拦截
            this.testFormSubmissionInterception();

            // 测试5: API集成测试
            await this.testAPIIntegration();

            // 生成测试报告
            this.generateTestReport();

            return this.testResults;
        }

        /**
         * 测试DOM元素
         */
        testDOMElements() {
            console.log('\n📋 测试1: DOM元素检查');
            
            const results = {
                test: 'DOM Elements',
                passed: 0,
                failed: 0,
                details: []
            };

            // 检查隐藏字段
            const hiddenField = document.getElementById('inchargeByBackendUserId');
            if (hiddenField) {
                results.passed++;
                results.details.push('✅ 隐藏负责人字段存在');
                console.log(`  字段ID: ${hiddenField.id}`);
                console.log(`  字段名称: ${hiddenField.name}`);
                console.log(`  字段类型: ${hiddenField.type}`);
                console.log(`  当前值: ${hiddenField.value || '(空)'}`);
            } else {
                results.failed++;
                results.details.push('❌ 隐藏负责人字段不存在');
            }

            // 检查表单
            const orderForm = document.getElementById('orderForm');
            if (orderForm) {
                results.passed++;
                results.details.push('✅ 订单表单存在');
                
                // 检查字段是否在表单内
                const fieldInForm = orderForm.querySelector('#inchargeByBackendUserId');
                if (fieldInForm) {
                    results.passed++;
                    results.details.push('✅ 负责人字段在表单内');
                } else {
                    results.failed++;
                    results.details.push('❌ 负责人字段不在表单内');
                }
            } else {
                results.failed++;
                results.details.push('❌ 订单表单不存在');
            }

            // 检查创建按钮
            const createBtn = document.getElementById('createOrder');
            if (createBtn) {
                results.passed++;
                results.details.push('✅ 创建订单按钮存在');
            } else {
                results.failed++;
                results.details.push('❌ 创建订单按钮不存在');
            }

            this.testResults.push(results);
            console.log(`  结果: ${results.passed}通过, ${results.failed}失败`);
        }

        /**
         * 测试字段值设置
         */
        testFieldValueSetting() {
            console.log('\n📝 测试2: 字段值设置');
            
            const results = {
                test: 'Field Value Setting',
                passed: 0,
                failed: 0,
                details: []
            };

            try {
                // 测试修复器是否存在
                if (window.responsiblePersonFixer) {
                    results.passed++;
                    results.details.push('✅ 负责人修复器已加载');

                    // 测试字段值获取
                    const userId = window.responsiblePersonFixer.getResponsiblePersonId();
                    if (userId) {
                        results.passed++;
                        results.details.push(`✅ 成功获取负责人ID: ${userId}`);
                    } else {
                        results.failed++;
                        results.details.push('❌ 无法获取负责人ID');
                    }

                    // 测试字段值设置
                    const setSuccess = window.responsiblePersonFixer.setResponsiblePersonField();
                    if (setSuccess) {
                        results.passed++;
                        results.details.push('✅ 成功设置负责人字段值');

                        // 验证字段值
                        const field = document.getElementById('inchargeByBackendUserId');
                        if (field && field.value) {
                            results.passed++;
                            results.details.push(`✅ 字段值验证通过: ${field.value}`);
                        } else {
                            results.failed++;
                            results.details.push('❌ 字段值验证失败');
                        }
                    } else {
                        results.failed++;
                        results.details.push('❌ 字段值设置失败');
                    }

                } else {
                    results.failed++;
                    results.details.push('❌ 负责人修复器未加载');
                }

            } catch (error) {
                results.failed++;
                results.details.push(`❌ 字段值设置测试异常: ${error.message}`);
            }

            this.testResults.push(results);
            console.log(`  结果: ${results.passed}通过, ${results.failed}失败`);
        }

        /**
         * 测试登录状态处理
         */
        testLoginStateHandling() {
            console.log('\n👤 测试3: 登录状态处理');
            
            const results = {
                test: 'Login State Handling',
                passed: 0,
                failed: 0,
                details: []
            };

            try {
                // 检查AppState
                if (window.OTA && window.OTA.appState) {
                    results.passed++;
                    results.details.push('✅ AppState系统存在');

                    // 检查登录状态
                    const isLoggedIn = window.OTA.appState.get('auth.isLoggedIn');
                    if (isLoggedIn) {
                        results.passed++;
                        results.details.push('✅ 用户已登录');

                        // 检查用户信息
                        const currentUser = window.OTA.appState.get('auth.user');
                        if (currentUser && currentUser.email) {
                            results.passed++;
                            results.details.push(`✅ 用户邮箱: ${currentUser.email}`);
                        } else {
                            results.failed++;
                            results.details.push('❌ 无法获取用户邮箱');
                        }

                        // 检查后台用户数据
                        const backendUsers = window.OTA.appState.get('systemData.backendUsers');
                        if (backendUsers && backendUsers.length > 0) {
                            results.passed++;
                            results.details.push(`✅ 后台用户数据: ${backendUsers.length}个用户`);
                        } else {
                            results.failed++;
                            results.details.push('❌ 后台用户数据缺失');
                        }

                    } else {
                        results.failed++;
                        results.details.push('❌ 用户未登录');
                    }

                } else {
                    results.failed++;
                    results.details.push('❌ AppState系统不存在');
                }

            } catch (error) {
                results.failed++;
                results.details.push(`❌ 登录状态测试异常: ${error.message}`);
            }

            this.testResults.push(results);
            console.log(`  结果: ${results.passed}通过, ${results.failed}失败`);
        }

        /**
         * 测试表单提交拦截
         */
        testFormSubmissionInterception() {
            console.log('\n🛡️ 测试4: 表单提交拦截');
            
            const results = {
                test: 'Form Submission Interception',
                passed: 0,
                failed: 0,
                details: []
            };

            try {
                // 测试按钮事件监听
                const createBtn = document.getElementById('createOrder');
                if (createBtn) {
                    const listeners = getEventListeners(createBtn);
                    if (listeners && listeners.click && listeners.click.length > 0) {
                        results.passed++;
                        results.details.push(`✅ 创建按钮有${listeners.click.length}个点击监听器`);
                    } else {
                        results.failed++;
                        results.details.push('⚠️ 无法检测按钮监听器（浏览器限制）');
                    }
                } else {
                    results.failed++;
                    results.details.push('❌ 创建按钮不存在');
                }

                // 测试表单事件监听
                const orderForm = document.getElementById('orderForm');
                if (orderForm) {
                    const listeners = getEventListeners(orderForm);
                    if (listeners && listeners.submit && listeners.submit.length > 0) {
                        results.passed++;
                        results.details.push(`✅ 表单有${listeners.submit.length}个提交监听器`);
                    } else {
                        results.failed++;
                        results.details.push('⚠️ 无法检测表单监听器（浏览器限制）');
                    }
                } else {
                    results.failed++;
                    results.details.push('❌ 订单表单不存在');
                }

                // 测试强制修复功能
                if (window.fixResponsiblePerson) {
                    const fixResult = window.fixResponsiblePerson();
                    if (fixResult) {
                        results.passed++;
                        results.details.push('✅ 强制修复功能可用');
                    } else {
                        results.failed++;
                        results.details.push('❌ 强制修复功能失败');
                    }
                } else {
                    results.failed++;
                    results.details.push('❌ 强制修复功能不存在');
                }

            } catch (error) {
                results.failed++;
                results.details.push(`❌ 表单拦截测试异常: ${error.message}`);
            }

            this.testResults.push(results);
            console.log(`  结果: ${results.passed}通过, ${results.failed}失败`);
        }

        /**
         * 测试API集成
         */
        async testAPIIntegration() {
            console.log('\n🔌 测试5: API集成测试');
            
            const results = {
                test: 'API Integration',
                passed: 0,
                failed: 0,
                details: []
            };

            try {
                // 检查API服务
                if (window.OTA && window.OTA.apiService) {
                    results.passed++;
                    results.details.push('✅ API服务存在');

                    // 测试获取默认用户ID方法
                    if (typeof window.OTA.apiService.getDefaultBackendUserId === 'function') {
                        results.passed++;
                        results.details.push('✅ getDefaultBackendUserId方法存在');

                        const userId = window.OTA.apiService.getDefaultBackendUserId();
                        if (userId) {
                            results.passed++;
                            results.details.push(`✅ API返回用户ID: ${userId}`);
                        } else {
                            results.failed++;
                            results.details.push('❌ API未返回有效用户ID');
                        }
                    } else {
                        results.failed++;
                        results.details.push('❌ getDefaultBackendUserId方法不存在');
                    }

                    // 测试系统数据
                    const systemData = window.OTA.appState && window.OTA.appState.get('systemData');
                    if (systemData) {
                        results.passed++;
                        results.details.push('✅ 系统数据存在');

                        if (systemData.backendUsers && systemData.backendUsers.length > 0) {
                            results.passed++;
                            results.details.push(`✅ 后台用户: ${systemData.backendUsers.length}个`);
                        } else {
                            results.failed++;
                            results.details.push('❌ 后台用户数据为空');
                        }
                    } else {
                        results.failed++;
                        results.details.push('❌ 系统数据不存在');
                    }

                } else {
                    results.failed++;
                    results.details.push('❌ API服务不存在');
                }

            } catch (error) {
                results.failed++;
                results.details.push(`❌ API集成测试异常: ${error.message}`);
            }

            this.testResults.push(results);
            console.log(`  结果: ${results.passed}通过, ${results.failed}失败`);
        }

        /**
         * 生成测试报告
         */
        generateTestReport() {
            console.log('\n📊 测试报告');
            console.log('='.repeat(50));

            let totalPassed = 0;
            let totalFailed = 0;

            this.testResults.forEach(result => {
                totalPassed += result.passed;
                totalFailed += result.failed;

                console.log(`\n${result.test}:`);
                result.details.forEach(detail => {
                    console.log(`  ${detail}`);
                });
            });

            console.log('\n📈 总计:');
            console.log(`  ✅ 通过: ${totalPassed}`);
            console.log(`  ❌ 失败: ${totalFailed}`);
            console.log(`  📊 成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

            // 给出修复建议
            if (totalFailed > 0) {
                console.log('\n🔧 修复建议:');
                if (totalFailed <= 2) {
                    console.log('  - 运行 fixResponsiblePerson() 尝试自动修复');
                } else {
                    console.log('  - 刷新页面并重新登录');
                    console.log('  - 运行 responsiblePersonFixer.runDiagnostics() 获取详细信息');
                }
            } else {
                console.log('\n🎉 所有测试通过！负责人字段修复成功！');
            }

            console.log('\n📋 验证订单创建:');
            console.log('  1. 填写必要的订单信息');
            console.log('  2. 点击"创建订单"按钮');
            console.log('  3. 系统应自动填充负责人字段');
            console.log('  4. 订单创建不应再出现"负责人为必填项"错误');
        }

        /**
         * 快速检查
         */
        quickCheck() {
            console.log('🔍 负责人字段快速检查:');
            
            const field = document.getElementById('inchargeByBackendUserId');
            console.log(`字段存在: ${field ? '✅' : '❌'}`);
            
            if (field) {
                console.log(`字段值: ${field.value || '(空)'}`);
            }

            const fixer = window.responsiblePersonFixer;
            console.log(`修复器存在: ${fixer ? '✅' : '❌'}`);

            if (fixer && !field?.value) {
                console.log('🔧 正在自动修复...');
                const success = fixer.forceSetResponsiblePerson();
                console.log(`修复结果: ${success ? '✅' : '❌'}`);
            }

            return field && field.value;
        }
    }

    // 创建全局测试器实例
    window.responsiblePersonTester = new ResponsiblePersonTester();

    // 提供快捷方法
    window.testResponsiblePerson = () => {
        return window.responsiblePersonTester.runFullTest();
    };

    window.quickCheckResponsiblePerson = () => {
        return window.responsiblePersonTester.quickCheck();
    };

    // 页面加载后自动运行快速检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                window.responsiblePersonTester.quickCheck();
            }, 2000);
        });
    } else {
        setTimeout(() => {
            window.responsiblePersonTester.quickCheck();
        }, 2000);
    }

    console.log('🧪 负责人字段测试器已加载');
    console.log('📋 可用命令:');
    console.log('  testResponsiblePerson() - 运行完整测试');
    console.log('  quickCheckResponsiblePerson() - 快速检查');

})();
