# 项目结构与变更说明

## 1. 主要目录与文件

- `index.html`：主页面结构，包含订单表单、预览、登录、状态栏等。
- `style.css`：全局样式，含响应式、主题、表单、预览等所有 UI 相关样式。
- `js/app-state.js`：全局状态管理，含用户、系统数据、订单、配置等。
- `js/api-service.js`：API 交互与本地静态数据、订单创建、数据预处理等。
- `js/ui-manager.js`：UI 交互主控，表单渲染、数据收集、预览、事件绑定等。
- `js/logger.js`：日志模块（已移除前端显示，仅保留后台调试输出）。
- 其他辅助模块：`js/utils.js`、`js/gemini-service.js` 等。

## 2. 重要字段与功能说明

### OTA 字段
- 新增 `ota_channel` 字段，支持“下拉选择+可编辑文本框”，每个账号可配置默认值和可选列表（本地静态映射，见 `app-state.js`）。
- 表单、数据收集、API 提交、预览等全流程支持 ota_channel 字段。

### 日志系统
- 日志控制台 UI 已彻底移除，仅保留后台调试控制台输出。
- 相关 UI 逻辑、按钮、面板、样式均已删除。

### 日期格式
- 所有日期字段（表单、预览、API、日志等）统一为 `YYYY-MM-DD` 格式。
- 相关格式化、校验、预处理逻辑已同步更新。

### UI 响应式与紧凑化
- 订单预览、表单、主界面等全部优化为高密度、紧凑布局，字段上下贴合。
- 针对 PC/平板/手机多端，padding、margin、字体、弹窗等均自适应收缩，单页尽可能显示全部内容。
- 兼容 Safari 的 backdrop-filter 样式已补充。

## 3. 依赖与扩展
- 所有新功能均未引入第三方依赖，全部为原生 JS/CSS 实现。
- 未来如需扩展 ota 配置为后端接口获取，可平滑替换本地静态映射。

---

> 本文档同步于 2024-06-25，涵盖本次所有核心结构与变更。如有新需求请及时补充。 