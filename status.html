<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - OTA订单处理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #4f46e5;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .status-card h3 {
            margin: 0 0 10px;
            color: #1e293b;
            font-size: 1.1rem;
        }
        .status-indicator {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        .status-online {
            background: #dcfce7;
            color: #166534;
        }
        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }
        .info-section {
            background: #f1f5f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .info-section h3 {
            margin: 0 0 15px;
            color: #1e293b;
        }
        .info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .btn {
            display: inline-block;
            background: #4f46e5;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #4338ca;
        }
        .timestamp {
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 OTA订单处理系统</h1>
            <p>GoMyHire Integration - 系统状态页面</p>
        </div>
        
        <div class="content">
            <div class="status-grid">
                <div class="status-card">
                    <h3>服务状态</h3>
                    <span class="status-indicator status-online">🟢 在线运行</span>
                </div>
                <div class="status-card">
                    <h3>部署状态</h3>
                    <span class="status-indicator status-online">✅ 部署成功</span>
                </div>
                <div class="status-card">
                    <h3>版本信息</h3>
                    <span class="status-indicator status-info">v1.0.0</span>
                </div>
            </div>

            <div class="info-section">
                <h3>📊 系统信息</h3>
                <ul class="info-list">
                    <li><span>项目名称</span><span>GMH Create Order</span></li>
                    <li><span>部署平台</span><span>Netlify</span></li>
                    <li><span>主域名</span><span>gmhcreateorder.netlify.app</span></li>
                    <li><span>最后更新</span><span id="lastUpdate">-</span></li>
                </ul>
            </div>

            <div class="info-section">
                <h3>🔧 功能特性</h3>
                <ul class="info-list">
                    <li><span>智能订单解析</span><span>✅ 支持</span></li>
                    <li><span>图片分析</span><span>✅ 支持</span></li>
                    <li><span>多订单管理</span><span>✅ 支持</span></li>
                    <li><span>历史记录</span><span>✅ 支持</span></li>
                    <li><span>多语言支持</span><span>✅ 中文/英文</span></li>
                </ul>
            </div>

            <div class="info-section">
                <h3>🌐 API连接</h3>
                <ul class="info-list">
                    <li><span>GoMyHire API</span><span>🟢 已连接</span></li>
                    <li><span>Gemini AI</span><span>🟢 已连接</span></li>
                    <li><span>图片识别</span><span>🟢 已启用</span></li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="/index.html" class="btn">🚀 启动系统</a>
            </div>

            <div class="timestamp">
                页面生成时间: <span id="currentTime">-</span>
            </div>
        </div>
    </div>

    <script>
        // 更新时间戳
        function updateTimestamp() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('lastUpdate').textContent = timeString;
        }
        
        // 页面加载时更新时间
        updateTimestamp();
        
        // 每30秒更新一次时间
        setInterval(updateTimestamp, 30000);
    </script>
</body>
</html>
