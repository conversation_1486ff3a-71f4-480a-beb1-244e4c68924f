<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .language-toggle {
            margin-bottom: 20px;
            text-align: center;
        }
        .language-toggle button {
            margin: 0 10px;
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .language-toggle button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>下拉菜单修复测试</h1>
        
        <div class="language-toggle">
            <button onclick="switchLanguage('zh')">中文</button>
            <button onclick="switchLanguage('en')">English</button>
        </div>

        <div class="form-group">
            <label for="serviceType" data-i18n="form.serviceType">服务类型</label>
            <select id="serviceType" data-i18n="form.serviceTypePlaceholder">
                <option value="" data-i18n="form.selectServiceType">请选择服务类型</option>
                <option value="2">接机服务</option>
                <option value="3">送机服务</option>
                <option value="4">包车服务</option>
            </select>
        </div>

        <div class="form-group">
            <label for="carType" data-i18n="form.carType">车型</label>
            <select id="carType" data-i18n="form.carTypePlaceholder">
                <option value="" data-i18n="form.selectCarType">请选择车型</option>
                <option value="1">Comfort 5 Seater</option>
                <option value="2">Premium 7 Seater</option>
                <option value="3">Luxury 4 Seater</option>
            </select>
        </div>

        <div class="form-group">
            <label for="otaChannel" data-i18n="form.otaChannel">OTA渠道</label>
            <select id="otaChannel" data-i18n="form.otaChannelPlaceholder">
                <option value="" data-i18n="form.selectOtaChannel">请选择OTA渠道</option>
                <option value="booking">Booking.com</option>
                <option value="agoda">Agoda</option>
                <option value="expedia">Expedia</option>
            </select>
        </div>

        <div class="form-group">
            <label for="currency">货币</label>
            <select id="currency">
                <option value="MYR">MYR</option>
                <option value="USD">USD</option>
                <option value="SGD">SGD</option>
                <option value="CNY">CNY</option>
            </select>
        </div>

        <div id="testResult" class="test-result" style="display: none;"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/i18n.js"></script>
    
    <script>
        // 初始化国际化
        let i18nManager;
        
        // 模拟翻译数据
        const translations = {
            zh: {
                'form.serviceType': '服务类型',
                'form.serviceTypePlaceholder': '请选择服务类型',
                'form.selectServiceType': '请选择服务类型',
                'form.carType': '车型',
                'form.carTypePlaceholder': '请选择车型',
                'form.selectCarType': '请选择车型',
                'form.otaChannel': 'OTA渠道',
                'form.otaChannelPlaceholder': '请选择OTA渠道',
                'form.selectOtaChannel': '请选择OTA渠道'
            },
            en: {
                'form.serviceType': 'Service Type',
                'form.serviceTypePlaceholder': 'Select service type',
                'form.selectServiceType': 'Select service type',
                'form.carType': 'Car Type',
                'form.carTypePlaceholder': 'Select car type',
                'form.selectCarType': 'Select car type',
                'form.otaChannel': 'OTA Channel',
                'form.otaChannelPlaceholder': 'Select OTA channel',
                'form.selectOtaChannel': 'Select OTA channel'
            }
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 创建国际化管理器实例
            if (window.I18nManager) {
                i18nManager = new window.I18nManager();
                // 设置翻译数据
                i18nManager.translations = translations;
                i18nManager.currentLanguage = 'zh';
                i18nManager.updateUI();
                
                showTestResult('初始化完成，请测试语言切换功能', 'success');
            } else {
                showTestResult('国际化管理器加载失败', 'error');
            }
        });

        function switchLanguage(lang) {
            if (i18nManager) {
                i18nManager.setLanguage(lang);
                
                // 检查下拉菜单选项是否还存在
                const serviceType = document.getElementById('serviceType');
                const carType = document.getElementById('carType');
                const otaChannel = document.getElementById('otaChannel');
                
                const serviceOptions = serviceType.options.length;
                const carOptions = carType.options.length;
                const otaOptions = otaChannel.options.length;
                
                if (serviceOptions > 1 && carOptions > 1 && otaOptions > 1) {
                    showTestResult(`✅ 语言切换成功！所有下拉菜单选项保持完整 (服务类型:${serviceOptions}项, 车型:${carOptions}项, OTA渠道:${otaOptions}项)`, 'success');
                } else {
                    showTestResult(`❌ 下拉菜单选项丢失！(服务类型:${serviceOptions}项, 车型:${carOptions}项, OTA渠道:${otaOptions}项)`, 'error');
                }
            }
        }

        function showTestResult(message, type) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.textContent = message;
            resultDiv.className = `test-result ${type}`;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
