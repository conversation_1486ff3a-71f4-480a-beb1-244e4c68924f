<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>负责人字段测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        select, input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>负责人字段测试页面</h1>
    
    <div class="test-section">
        <h2>系统状态检查</h2>
        <button onclick="checkSystemStatus()">检查系统状态</button>
        <div id="systemStatus"></div>
    </div>

    <div class="test-section">
        <h2>负责人字段测试</h2>
        <select id="inchargeByBackendUserId">
            <option value="">请选择负责人</option>
        </select>
        
        <button onclick="testResponsiblePersonField()">测试负责人字段设置</button>
        <button onclick="forceSetDefaultUser()">强制设置默认用户</button>
        <button onclick="simulateFormCollection()">模拟表单收集</button>
        
        <div id="responsiblePersonResult"></div>
    </div>

    <div class="test-section">
        <h2>表单数据测试</h2>
        <textarea id="testOrderData" rows="10" placeholder="粘贴订单数据进行测试...">
测试订单：北京到上海
出发时间：2024-01-15 09:00
价格：500
车型：商务车
联系人：张先生 13800138000
        </textarea>
        
        <button onclick="testFormDataCollection()">测试表单数据收集</button>
        <button onclick="testOrderCreation()">测试订单创建</button>
        
        <div id="formTestResult"></div>
    </div>

    <div class="test-section">
        <h2>调试日志</h2>
        <button onclick="clearLogs()">清空日志</button>
        <button onclick="exportLogs()">导出日志</button>
        <pre id="debugLogs"></pre>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/managers/form-manager.js"></script>

    <script>
        // 初始化测试环境
        let testLogger;
        let uiManager;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 初始化日志系统
                testLogger = getLogger();
                testLogger.log('负责人字段测试页面初始化', 'info');
                
                // 创建模拟的UI管理器
                const mockElements = {
                    inchargeByBackendUserId: document.getElementById('inchargeByBackendUserId')
                };
                
                // 初始化表单管理器
                if (window.OTA && window.OTA.managers && window.OTA.managers.FormManager) {
                    const formManager = new window.OTA.managers.FormManager(mockElements);
                    formManager.init();
                    
                    // 将表单管理器附加到全局对象以便测试
                    window.testFormManager = formManager;
                }
                
                updateDebugLogs();
                checkSystemStatus();
                
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('systemStatus').innerHTML = 
                    `<div class="status error">初始化失败: ${error.message}</div>`;
            }
        });

        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let statusHtml = '';
            
            try {
                // 检查AppState
                const appState = getAppState();
                const isLoggedIn = appState.get('auth.isLoggedIn');
                const user = appState.get('auth.user');
                const systemData = appState.get('systemData');
                
                statusHtml += `<div class="status ${isLoggedIn ? 'success' : 'warning'}">
                    登录状态: ${isLoggedIn ? '已登录' : '未登录'}
                </div>`;
                
                if (user) {
                    statusHtml += `<div class="status success">
                        当前用户: ${user.email || user.name || '未知'}
                    </div>`;
                }
                
                // 检查系统数据
                if (systemData && systemData.backendUsers) {
                    statusHtml += `<div class="status success">
                        后端用户数据: ${systemData.backendUsers.length} 个用户
                    </div>`;
                } else {
                    statusHtml += `<div class="status warning">
                        后端用户数据: 未加载
                    </div>`;
                }
                
                // 检查API服务
                const apiService = getApiService();
                if (apiService) {
                    const defaultUserId = apiService.getDefaultBackendUserId();
                    statusHtml += `<div class="status ${defaultUserId ? 'success' : 'warning'}">
                        默认用户ID: ${defaultUserId || '未设置'}
                    </div>`;
                } else {
                    statusHtml += `<div class="status error">API服务未初始化</div>`;
                }
                
                // 显示系统数据详情
                if (systemData) {
                    statusHtml += `<pre>系统数据详情:
${JSON.stringify({
    backendUsers: (systemData.backendUsers || []).length,
    carTypes: (systemData.carTypes || []).length,
    subCategories: (systemData.subCategories || []).length
}, null, 2)}</pre>`;
                }
                
            } catch (error) {
                statusHtml += `<div class="status error">状态检查失败: ${error.message}</div>`;
            }
            
            statusDiv.innerHTML = statusHtml;
        }

        function testResponsiblePersonField() {
            const resultDiv = document.getElementById('responsiblePersonResult');
            let resultHtml = '';
            
            try {
                const selectElement = document.getElementById('inchargeByBackendUserId');
                const currentValue = selectElement.value;
                
                resultHtml += `<div class="status info">当前选中值: ${currentValue || '无'}</div>`;
                
                // 测试获取默认用户ID
                const apiService = getApiService();
                if (apiService) {
                    const defaultUserId = apiService.getDefaultBackendUserId();
                    resultHtml += `<div class="status ${defaultUserId ? 'success' : 'warning'}">
                        默认用户ID: ${defaultUserId || '未找到'}
                    </div>`;
                    
                    // 测试用户映射逻辑
                    const user = getAppState().get('auth.user');
                    if (user) {
                        const systemData = getAppState().get('systemData');
                        if (systemData && systemData.backendUsers) {
                            const matchedUser = systemData.backendUsers.find(u => 
                                u.email && user.email && u.email.toLowerCase() === user.email.toLowerCase()
                            );
                            
                            resultHtml += `<div class="status ${matchedUser ? 'success' : 'warning'}">
                                用户映射: ${matchedUser ? `找到匹配用户 (ID: ${matchedUser.id})` : '未找到匹配用户'}
                            </div>`;
                        }
                    }
                }
                
                // 显示选项列表
                const options = Array.from(selectElement.options).map(opt => ({
                    value: opt.value,
                    text: opt.textContent
                }));
                
                resultHtml += `<pre>可用选项:
${JSON.stringify(options, null, 2)}</pre>`;
                
            } catch (error) {
                resultHtml += `<div class="status error">测试失败: ${error.message}</div>`;
            }
            
            resultDiv.innerHTML = resultHtml;
        }

        function forceSetDefaultUser() {
            try {
                const apiService = getApiService();
                const defaultUserId = apiService.getDefaultBackendUserId();
                const selectElement = document.getElementById('inchargeByBackendUserId');
                
                if (defaultUserId && selectElement) {
                    selectElement.value = defaultUserId;
                    testLogger.log('强制设置默认用户成功', 'success', { userId: defaultUserId });
                } else {
                    testLogger.log('强制设置默认用户失败', 'error', {
                        hasUserId: !!defaultUserId,
                        hasElement: !!selectElement
                    });
                }
                
                testResponsiblePersonField();
                updateDebugLogs();
            } catch (error) {
                testLogger.logError('强制设置默认用户异常', error);
                updateDebugLogs();
            }
        }

        function simulateFormCollection() {
            try {
                if (window.testFormManager) {
                    const formData = window.testFormManager.collectFormData();
                    
                    document.getElementById('responsiblePersonResult').innerHTML = 
                        `<div class="status success">表单数据收集成功</div>
                        <pre>收集的数据:
${JSON.stringify(formData, null, 2)}</pre>`;
                } else {
                    document.getElementById('responsiblePersonResult').innerHTML = 
                        `<div class="status error">表单管理器未初始化</div>`;
                }
            } catch (error) {
                document.getElementById('responsiblePersonResult').innerHTML = 
                    `<div class="status error">表单数据收集失败: ${error.message}</div>`;
            }
        }

        function testFormDataCollection() {
            const resultDiv = document.getElementById('formTestResult');
            
            try {
                // 模拟表单数据
                const testData = {
                    order_detail: document.getElementById('testOrderData').value,
                    pickup_time: '2024-01-15T09:00:00',
                    car_type_id: 1,
                    sub_category_id: 1,
                    price: 500,
                    currency: 'CNY'
                };
                
                // 使用FormManager收集数据
                if (window.testFormManager) {
                    const collectedData = window.testFormManager.collectFormData();
                    
                    resultDiv.innerHTML = `
                        <div class="status success">表单数据收集完成</div>
                        <h4>收集的数据:</h4>
                        <pre>${JSON.stringify(collectedData, null, 2)}</pre>
                        <h4>负责人字段检查:</h4>
                        <div class="status ${collectedData.incharge_by_backend_user_id ? 'success' : 'error'}">
                            负责人ID: ${collectedData.incharge_by_backend_user_id || '未设置'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="status error">表单管理器未初始化</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">测试失败: ${error.message}</div>`;
            }
        }

        function testOrderCreation() {
            const resultDiv = document.getElementById('formTestResult');
            
            try {
                // 收集表单数据
                const formData = window.testFormManager ? window.testFormManager.collectFormData() : {};
                
                // 检查验证
                const apiService = getApiService();
                if (apiService && apiService.validateOrderData) {
                    const validation = apiService.validateOrderData(formData);
                    
                    resultDiv.innerHTML = `
                        <div class="status ${validation.isValid ? 'success' : 'error'}">
                            订单验证: ${validation.isValid ? '通过' : '失败'}
                        </div>
                        ${validation.errors ? `<div class="status error">错误: ${validation.errors.join(', ')}</div>` : ''}
                        <h4>表单数据:</h4>
                        <pre>${JSON.stringify(formData, null, 2)}</pre>
                        <h4>验证结果:</h4>
                        <pre>${JSON.stringify(validation, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="status error">API服务验证方法不可用</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">订单创建测试失败: ${error.message}</div>`;
            }
        }

        function updateDebugLogs() {
            const debugDiv = document.getElementById('debugLogs');
            if (testLogger && testLogger.getLogs) {
                const logs = testLogger.getLogs();
                debugDiv.textContent = logs.map(log => 
                    `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}${log.data ? ' | ' + JSON.stringify(log.data) : ''}`
                ).join('\n');
            }
        }

        function clearLogs() {
            if (testLogger && testLogger.clearLogs) {
                testLogger.clearLogs();
            }
            document.getElementById('debugLogs').textContent = '';
        }

        function exportLogs() {
            const logs = document.getElementById('debugLogs').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `responsible-person-test-logs-${Date.now()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 定期更新日志显示
        setInterval(updateDebugLogs, 2000);
    </script>
</body>
</html>
